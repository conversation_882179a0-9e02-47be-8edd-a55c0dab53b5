# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

YogaBot Live is a comprehensive SaaS platform that enables yoga studios to deploy AI-powered chatbots on their websites for customer engagement and lead generation. The platform consists of three main components:

1. **Studio Dashboard** - Web application for yoga studio owners to manage chatbots, view analytics, and handle leads
2. **Chatbot Widget** - Embeddable JavaScript widget that provides AI-powered customer support on studio websites  
3. **Admin Panel** - Platform administration interface for managing global settings, plans, and system health

## Development Status

⚠️ **Important**: This repository currently contains only planning and design documentation. No code implementation exists yet.

## Key Architecture Components

### User Roles & Permissions
- **Admin**: Platform owner with full system access and global configuration control
- **Studio**: Customer who owns the account, manages billing, staff, and chatbot configurations  
- **Staff**: Invited by Studio with view-only access to conversations and leads
- **Visitor**: End users interacting with deployed chatbots

### Core Features to Implement

#### Authentication & Account Management
- Social Sign-On (Google) as primary authentication method
- Multi-tenant architecture supporting Studios and Staff
- Razorpay integration for subscription billing and payment processing
- Staff invitation system with email-based onboarding

#### Chatbot Management System
- Multi-chatbot support per Studio account
- Persona-based conversations with random assignment
- Knowledge Base management with character limits per plan
- Domain locking for security (authorized domains only)
- Appearance customization (themes, colors, positioning)
- Tool integration system (Email Tool, future Calendar Tool)

#### Analytics & Monitoring
- Real-time conversation tracking and storage
- Lead capture and export functionality (CSV)
- Web analytics integration (pageviews, referrals, session data)
- Usage limit monitoring and notifications
- Plan-based feature restrictions

#### LLM Integration
- Multi-provider support (OpenAI, Google Gemini, Anthropic)
- BYOK (Bring Your Own Key) functionality with validation
- Global system prompt with draft/live versioning
- Context window management and conversation truncation
- Rate limiting and abuse protection

### Technical Requirements

#### Security & Compliance
- Domain validation before chatbot initialization (`window.location.hostname` checking)
- PII encryption at rest for leads and credentials
- Prompt injection hardening in global system prompt
- SMTP configuration for platform emails
- API key validation for BYOK implementations

#### Performance & Scalability
- Asynchronous task processing (message queues recommended)
- Database design supporting multi-tenant relationships
- Proper indexing on IDs and timestamps
- Rate limiting per IP address
- Lazy loading and skeleton screens for optimal UX

#### Mobile-First Design Principles
- Touch-friendly interfaces (44px minimum touch targets)
- Progressive disclosure for complex data
- Offline functionality with graceful degradation
- Responsive widget that doesn't impact host website performance

### Development Workflow (When Implementation Begins)

Since no package.json or build system exists yet, establish these commands when setting up the project:

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run test         # Run test suite
npm run test:watch   # Run tests in watch mode

# Code Quality  
npm run lint         # Run ESLint
npm run typecheck    # Run TypeScript checking
npm run format       # Format code with Prettier

# Database
npm run db:migrate   # Run database migrations
npm run db:seed      # Seed development data
npm run db:reset     # Reset database
```

## Implementation Priorities

When beginning development, focus on these areas in order:

1. **Authentication & Multi-tenancy Setup** - Establish user roles and account structure
2. **Basic Chatbot Widget** - Create embeddable widget with LLM integration
3. **Studio Dashboard Core** - Implement chatbot creation and basic management
4. **Conversation Storage & Analytics** - Build monitoring and lead capture
5. **Billing Integration** - Add Razorpay subscription management
6. **Advanced Features** - Personas, tools, advanced analytics

## Key Design Considerations

### User Experience Focus
- Mobile-first approach with progressive enhancement
- Accessibility compliance (WCAG 2.1 AA)
- Contextual help and guided onboarding flows
- Error handling with clear recovery paths

### Business Logic
- Plan-based feature restrictions and usage limits
- Automated email notifications for usage thresholds
- Lead anonymization when chatbots are deleted
- Grace period handling for payment failures

### Integration Points
- Razorpay for subscription management and billing
- Multiple LLM providers with fallback strategies
- Social login providers (Google OAuth)
- SMTP services for transactional emails
- CSV export functionality for lead management

## Documentation References

- `prd.md` - Complete product requirements and feature specifications
- `tmp_rovodev_ux_analysis.md` - User experience guidelines and journey mapping
- `tmp_rovodev_wireframes_flows.md` - Detailed wireframes and user interface flows

When implementing features, always reference these documents to ensure alignment with the planned user experience and business requirements.