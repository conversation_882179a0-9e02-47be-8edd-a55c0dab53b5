# YogaBot Live - Master Feature Document

This document outlines the high-level goals and features for the YogaBot Live SaaS platform.

## PART 1: THE SAAS PLATFORM & STUDIO DASHBOARD

This is the core product that the `Studio` (the customer) interacts with.

**1.1 User Roles & Permissions**
*   **`Admin`**: The platform owner with full system access.
*   **`Studio`**: The customer who owns the account. Manages billing, staff, and all chatbot configurations.
*   **`Staff`**: Invited by a `Studio`. For <PERSON>, this role has **view-only access** to the `Conversations` and `Leads` pages.

**1.2 Platform & Account Management**
*   **`Studio` Level - Account & Onboarding:**
    *   **Sign-Up/Login:** The primary method for account creation and access will be via **Social Sign-On** (e.g., Google).
    *   **Onboarding:** Upon first login, a new `Studio` is greeted by a simple onboarding checklist to guide their setup.
    *   **Staff Management:**
        *   A dedicated "Staff" page in the `Studio`'s settings.
        *   An "Invite Staff" button that sends an email invitation to the new member's address.
        *   The page will list all pending and active `Staff` members.
        *   A `Studio` can `Revoke Access` for any `Staff` member at any time.

*   **`Studio` Level - Billing & Subscription:**
    *   **Integration:** Uses **Razorpay** for subscription management.
    *   **Lifecycle Management:**
        *   **Upgrade/Downgrade:** A `Studio` can change their plan at any time. The change will be pro-rated and take effect immediately.
        *   **Payment Failure:** If a recurring payment fails, the account enters a "Grace Period" (e.g., 7 days). The `Studio` is notified via email. If the payment is not rectified within this period, the account is automatically downgraded to a "Read-Only" or "Inactive" state, and their chatbots are disabled.
        *   **Invoice History:** A section in their billing settings where a `Studio` can view and download all past invoices.

*   **`Admin` Level - Global Configuration:**
    *   **Global System Prompt:** The `Admin` controls a single, global system prompt.
        *   **Versioning:** The system will support a `Draft` and `Live` version of the prompt. The `Admin` can edit the `Draft` and then "Publish" it to make it `Live`, preventing accidental issues from live edits.
    *   **Default LLM Provider:** The `Admin` can set the default LLM provider (e.g., OpenAI, Google Gemini) to be used for all non-BYOK `Studios`.

*   **`Admin` Level - Plan Management:** The `Admin` has a dedicated panel to create and manage subscription plans. Each plan defines the limits and access for:
    *   Monthly Conversation Limit
    *   Monthly Message Limit
    *   Messages Per Conversation Limit
    *   Max Chatbots
    *   Max Personas
    *   KB Character Limit
    *   WPM Delay Feature (Enabled/Disabled)
    *   Custom Branding (Enabled/Disabled)
    *   Tool Availability (e.g., Email Tool, future Calendar Tool)
    *   **BYOK (Bring Your Own Key):**
        *   **Validation:** When a `Studio` enters their own LLM API key, a "Test Connection" button will be present. Clicking it will validate that the key is active and working before it can be saved.

*   **`Admin` Level - Platform Core Settings:**
    *   **Purpose:** This is a secure, top-level settings area in the `Admin`'s super panel. It is the central control room for configuring the entire YogaBot Live platform's core services. These are the master keys to the application.
    *   **Components:**
        *   **Payment Gateway Configuration:**
            *   Fields to input and manage the platform's master **Razorpay API Key** and **Secret Key**.
            *   A toggle for `Live`/`Test` mode.
        *   **LLM Provider Configuration:**
            *   Secure fields to input the platform's default API keys for each integrated LLM provider (e.g., OpenAI API Key, Google AI API Key, Anthropic API Key).
            *   A dropdown menu to select the **Default Model** for each provider (from available models by fetching from the API) that will be used for all non-BYOK `Studios`.
        *   **Social Login Configuration:**
            *   Fields to input the API credentials required for social sign-on functionality (e.g., Google `Client ID` and `Client Secret`).
        *   **Platform Email (SMTP) Configuration:**
            *   Secure fields for the `Admin` to configure the platform's own SMTP server.
            *   This is used for sending all system-wide, non-chatbot emails, such as `Staff` invitations and `Studio` usage notifications (e.g., from `<EMAIL>`).

**1.3 Chatbot Management**
This is a central feature of the platform.

*   **Chatbots Page (List View):**
    *   A page displaying all created chatbots as individual "cards".
    *   Each card shows the chatbot `Name`, `Status (Active/Inactive) (with toggle)`, and an options menu (`...`) with `Edit`, `Get Code`, and `Delete`.
    *   **Deletion Logic:** When a `Studio` deletes a chatbot, a confirmation modal appears. Upon confirmation, the chatbot and its specific configuration (KB, appearance settings) are **hard-deleted**. The associated conversation history will be anonymized and unlinked, but retained for historical analytics.
    *   A prominent **"Create New Chatbot"** button.
*   **Create Chatbot Flow:**
    1.  Clicking "Create New Chatbot" opens a simple modal popup.
    2.  **Modal Fields:** `Chatbot Name`, `Widget Position on Screen` (e.g., Bottom-Right, Bottom-Left), `Primary Color`.
    3.  On submission, the chatbot is created and the `Studio` is taken to the full Chatbot Edit Page.
*   **Chatbot Edit Page (Full Page Editor):**
    *   This is a detailed, multi-tabbed interface for fine-tuning a specific chatbot.
    *   **Tab 1: Configuration**
        *   `Chatbot Name`
        *   `Knowledge Base (KB)`: The main text area for this bot's specific knowledge.
        *   `Persona Selection`: A multi-select dropdown to assign one or more `Active` personas to this bot. **Logic:** If multiple personas are assigned, the system will **randomly select one** for each new `Visitor` conversation session. (Personas are created and managed in the dedicated 'Personas' section).
    *   **Tab 2: Appearance & Behavior**
        *   **Greeting:** Configure the proactive greeting message (e.g., "Welcome! How can I help?") and the trigger (e.g., "Show after 5 seconds").
        *   **Themes:** Choose from multiple pre-built design templates (e.g., images of pre-built chatboxes).
        *   **Launcher Widget:** Customize the little bubble that opens the chat.
            *   `Icon Library`: Select from a set of pre-made icons.
            *   `Icon Shape`: Circle, Square, Rounded Square.
        *   **Chat Window:** `Color`.
        *   `Custom Branding`: Toggle to remove "Powered by YogaBot" (per plan).
    *   **Tab 3: Tools**
        *   A list of all tools enabled by the `Studio`'s plan.
        *   Simple ON/OFF toggles to activate specific tools (like the `Email Tool`) for this specific chatbot.
    *   **Tab 4: Target & Deploy**
        *   **Domain Locking:** A critical security feature. A list where the `Studio` must add the authorized domains where this chatbot is allowed to appear (e.g., `myyogastudio.com`, `booking.yogaschool.com`). The chatbot script will not run on any other domain.
        *   **Get Code:** A button that generates the final Javascript snippet.
            *   **Clear Instructions:** The generated code will be presented alongside clear, simple instructions, including a visual example of exactly where to paste it in a website's HTML (e.g., "Paste this code right before the closing `</body>` tag of your website").

**1.4 Monitoring & Analytics**
This section is composed of four distinct pages, each with a clear purpose.

*   **A. The Dashboard Page (Chatbot Performance Overview):**
    *   **Purpose:** To provide the `Studio` with an at-a-glance, 30-second overview of their chatbot's effectiveness.
    *   **Components:**
        *   **Time Filter:** A master filter for the entire page (e.g., "Today", "Last 7 Days", "This Month").
        *   **KPI Widgets:** Large, clear displays for core metrics: `Total Conversations`, `Leads Captured`, and `Conversion Rate (%)`.
        *   **Leads & Conversations Trend Chart:** A line graph comparing the number of conversations to the number of leads captured over the selected time period.
        *   **Recent Leads List:** A feed showing the last 5-10 leads captured, with their name and email, for immediate action.
        *   **Top Topics/Intents:** A simple list or tag cloud of the most frequently asked questions or topics (e.g., "Pricing," "Class Schedule," "Vinyasa"), extracted by the LLM to provide business insights.

*   **B. The Web Analytics Page:**
    *   **Purpose:** To provide foundational website traffic metrics, adding value beyond the chatbot itself.
    *   **Implementation Note:** This requires the deployed Javascript snippet to be a more comprehensive analytics tracker, not just a chatbot launcher.
    *   **Components:**
        *   **Time Filter:** A master filter for the page.
        *   **Core Traffic Metrics:** Widgets for `Unique Visitors`, `Total Pageviews`, and `Average Session Duration`.
        *   **Top Referral Sources:** A table or pie chart showing where traffic is coming from (e.g., Google, Instagram, Direct, specific referring domains).
        *   **Most Visited Pages:** A list of the most popular pages on their website, helping them understand user flow.

*   **C. The Conversations Page:**
    *   **Purpose:** To provide a complete, searchable archive of every single conversation for detailed review and quality assurance.
    *   **Components:**
        *   **A searchable, filterable table** of all conversations.
        *   **Table Columns:** `Visitor ID`, `Assigned Persona`, `Start Date & Time`, `Number of Messages`, `Status` (e.g., Completed, Captured Lead, Abandoned), `Source Chatbot`.
        *   **Functionality:** Powerful `Search` (by keyword) and `Filtering` (by date range, chatbot, or persona). Clicking any row opens the **full, unabridged conversation transcript** for that session.

*   **D. The Leads Page:**
    *   **Purpose:** To provide a clean, actionable list of sales-ready leads. This page is optimized for conversion.
    *   **Components:**
        *   **A clean, searchable table** focused only on visitors who provided contact information.
        *   **Table Columns:** `Name`, `Email`, `Phone Number` (if captured), `Date Captured`, `Source Chatbot`.
        *   **Functionality:**
            *   **View Conversation Link:** A button on each row that links directly to the full transcript on the Conversations Page.
            *   **Export to CSV:** A crucial button allowing the `Studio` to download their lead list for use in external marketing software (e.g., Mailchimp).

**1.5 Personas Management**
*   **Purpose:** To provide a central library where the `Studio` can create, manage, and fine-tune the personas used across their different chatbots.
*   **Components:**
    *   **Personas Page (List View):** A page displaying all created personas as individual "cards." Each card shows the `Persona Name` and `Status (Active/Inactive)`.
    *   **"Create New Persona" Button:** Opens a page or modal for creating a new persona.
*   **Persona Editor:**
    *   `Persona Name` (e.g., "Maya," "David")
    *   `Persona Role/Description` (e.g., "A friendly and helpful studio guide who loves Vinyasa yoga.")
    *   **WPM Delay Configuration:** Dedicated fields for `Reading WPM Delay` and `Writing WPM Delay` for this specific persona.
    *   `Status` Toggle: `Active`/`Inactive`. An inactive persona cannot be assigned to a chatbot.

---

## PART 2: THE CHATBOT WIDGET (VISITOR EXPERIENCE)

*   **Dynamic Persona:** Greets visitors with a randomly assigned active persona.
*   **Appearance:** Renders with all the customization options selected in the Chatbot Edit Page.
*   **Offline Mode:** If backend services fail, the widget displays an offline message and a simple contact form.
*   **Performance:** The Javascript snippet must load asynchronously to not impact the host website's performance.
*   **Responsiveness:** The widget must be fully functional and non-intrusive on all mobile devices.

---

## PART 3: SYSTEM ARCHITECTURE & NON-FUNCTIONAL REQUIREMENTS

This section outlines critical "deep thinking" aspects for the developer.

*   **Security & Compliance**
    *   **Domain Locking Logic:** The chatbot's Javascript must verify `window.location.hostname` against the authorized list before initializing.
    *   **Data Encryption:** All PII (leads) and credentials (SMTP, BYOK keys) must be securely encrypted at rest in the database.
    *   **Prompt Injection Hardening:** The global system prompt must be engineered to maintain control and ignore malicious `Visitor` instructions.
    *   **Legal Pages:** Framework for `Admin` to add ToS/Privacy Policy.
*   **Scalability & Performance**
    *   **Asynchronous Tasks:** Use a message queue (like RabbitMQ) or a real-time service (like Ably) for all background tasks (sending emails) to keep the chat UI snappy.
    *   **Database Schema & Indexing:** The database must be designed to support the new relationships (`Studio` -> has many `Chatbots` -> has many authorized `Domains`). Proper indexing on IDs and timestamps is required from day one.
    *   **Rate Limiting:** Protect against abuse by limiting requests from a single IP address.
*   **Logging, Admin & Support Tooling**
    *   **`Admin` - System Health Dashboard:** A panel for the `Admin` to monitor the status of core services (Database, Razorpay API, LLM API).
    *   **`Admin` - Secure Logging:** A system for logging crucial debug information (like the final assembled prompt, with all PII scrubbed out) to diagnose bot behavior.
    *   **`Admin` - Impersonation:** The ability for the `Admin` to "Login as Studio" is a critical support feature.
*   **System Logic**
    *   **Usage Notifications:** Automated emails to the `Studio` when they approach their plan's usage limits.
    *   **LLM Token Management:** A strategy to truncate the oldest parts of the conversation history to ensure the final prompt always fits within the LLM's context window.
